<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ImportCsvForDemo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'insights:import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import script for risk insights demo';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // import the organisations
        

        return Command::SUCCESS;
    }
}
