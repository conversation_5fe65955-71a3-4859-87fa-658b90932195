<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organisation;
use App\Models\Broker;
use App\Models\Sector;
use Carbon\Carbon;
use Exception;

class ImportCsvForDemo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'insights:import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import script for risk insights demo - imports organisation data from CSV';

    /**
     * CSV file path relative to public directory
     *
     * @var string
     */
    protected $csvFilePath = 'risk-insights-csv/organisations.csv';

    /**
     * Statistics for import process
     *
     * @var array
     */
    protected $stats = [
        'total_rows' => 0,
        'imported' => 0,
        'updated' => 0,
        'errors' => 0,
        'error_details' => []
    ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting organisation CSV import...');
        $this->info('CSV file path: ' . public_path($this->csvFilePath));

        // Confirm before proceeding
        if (!$this->confirm('Do you want to proceed with the import? This will create/update organisation records.')) {
            $this->info('Import cancelled by user.');
            return Command::SUCCESS;
        }

        try {
            $this->importOrganisations();
            $this->displayResults();
        } catch (Exception $e) {
            $this->error('Import failed: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Import organisations from CSV file
     *
     * @return void
     */
    protected function importOrganisations()
    {
        $csvPath = public_path($this->csvFilePath);

        if (!file_exists($csvPath)) {
            throw new Exception("CSV file not found at: {$csvPath}");
        }

        $handle = fopen($csvPath, 'r');

        if ($handle === false) {
            throw new Exception("Unable to open CSV file: {$csvPath}");
        }

        // Read header row
        $headers = fgetcsv($handle);
        if ($headers === false) {
            throw new Exception("Unable to read CSV headers");
        }

        $this->info("CSV headers found: " . implode(', ', $headers));

        // Count total rows for progress bar
        $totalRows = 0;
        while (fgetcsv($handle) !== false) {
            $totalRows++;
        }
        rewind($handle);
        fgetcsv($handle); // Skip header again

        $this->info("Found {$totalRows} data rows to process");

        // Create progress bar
        $progressBar = $this->output->createProgressBar($totalRows);
        $progressBar->setFormat('verbose');
        $progressBar->start();

        // Process each data row
        $rowNumber = 1; // Start from 1 (header is row 0)

        while (($data = fgetcsv($handle)) !== false) {
            $rowNumber++;
            $this->stats['total_rows']++;

            if (empty(array_filter($data))) {
                $progressBar->advance();
                continue; // Skip empty rows
            }

            try {
                $mappedData = array_combine($headers, $data);
                $this->processOrganisationRow($mappedData, $rowNumber);

            } catch (Exception $e) {
                $this->stats['errors']++;
                $this->stats['error_details'][] = "Row {$rowNumber}: " . $e->getMessage();
                // Don't show individual errors during progress to keep output clean
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        fclose($handle);
    }

    /**
     * Process a single organisation row from CSV
     *
     * @param array $data
     * @param int $rowNumber
     * @return void
     */
    protected function processOrganisationRow(array $data, int $rowNumber)
    {
        // Validate required fields
        if (empty($data['client_name'])) {
            throw new Exception("Missing client_name");
        }

        // Map CSV data to Organisation fields
        $organisationData = $this->mapCsvToOrganisation($data);

        // Check if organisation already exists (by name)
        $existingOrg = Organisation::where('name', $organisationData['name'])->first();

        if ($existingOrg) {
            // Update existing organisation
            $existingOrg->update($organisationData);
            $this->stats['updated']++;
        } else {
            // Create new organisation
            Organisation::create($organisationData);
            $this->stats['imported']++;
        }
    }

    /**
     * Map CSV data to Organisation model fields
     *
     * @param array $csvData
     * @return array
     */
    protected function mapCsvToOrganisation(array $csvData): array
    {
        $data = [
            'name' => trim($csvData['client_name']),
            'product_subsector' => !empty($csvData['subsector']) ? trim($csvData['subsector']) : null,
        ];

        // Handle sector lookup/creation
        if (!empty($csvData['sector'])) {
            $sectorId = $this->findSectorByName(trim($csvData['sector']));
            if ($sectorId) {
                $data['sector'] = $sectorId;
            }
        }

        // Handle broker lookup
        if (!empty($csvData['Broker'])) {
            $brokerId = $this->findBrokerByName(trim($csvData['Broker']));
            if ($brokerId) {
                $data['broker_id'] = $brokerId;
            }
        }

        // Handle renewal date
        if (!empty($csvData['Renewal Date'])) {
            $renewalDate = $this->parseDate($csvData['Renewal Date']);
            if ($renewalDate) {
                $data['expiry_date_of_cover'] = $renewalDate->format('Y-m-d');
            }
        }

        // Handle risk score
        if (!empty($csvData['risk_score']) && is_numeric($csvData['risk_score'])) {
            $data['risk_grading'] = (int) $csvData['risk_score'];
        }

        // Set default values for required fields that might be missing
        $data = array_merge([
            'bound' => 0,
            'status' => 0, // Status is an integer field (0 = default)
            'country' => 'GB', // Default to UK
            'address_line_1' => '',
            'postcode' => '',
            'phone' => '',
            'email' => '',
        ], $data);

        return $data;
    }

    /**
     * Find sector by name or create if it doesn't exist
     *
     * @param string $sectorName
     * @return int|null
     */
    protected function findSectorByName(string $sectorName): ?int
    {
        // Try to find existing sector by handle or type
        $sector = Sector::where('handle', 'LIKE', '%' . $sectorName . '%')
                       ->orWhere('type', 'LIKE', '%' . $sectorName . '%')
                       ->first();

        if (!$sector) {
            // Create sector if it doesn't exist
            $sector = Sector::create([
                'handle' => $sectorName,
                'type' => $sectorName,
            ]);
        }

        return $sector->id;
    }

    /**
     * Find broker by name
     *
     * @param string $brokerName
     * @return int|null
     */
    protected function findBrokerByName(string $brokerName): ?int
    {
        $broker = Broker::where('name', 'LIKE', '%' . $brokerName . '%')->first();

        if (!$broker) {
            // Create broker if it doesn't exist
            $broker = Broker::create([
                'name' => $brokerName,
                'address_1' => '',
                'email' => '',
                'phone' => '',
            ]);
        }

        return $broker->id;
    }

    /**
     * Parse date from CSV format (d/m/Y)
     *
     * @param string $dateString
     * @return Carbon|null
     */
    protected function parseDate(string $dateString): ?Carbon
    {
        try {
            // Try parsing d/m/Y format first (e.g., "14/3/2026")
            if (preg_match('/^\d{1,2}\/\d{1,2}\/\d{4}$/', $dateString)) {
                return Carbon::createFromFormat('d/m/Y', $dateString);
            }

            // Fallback to Carbon's general parsing
            return Carbon::parse($dateString);

        } catch (Exception $e) {
            $this->warn("Unable to parse date: {$dateString}");
            return null;
        }
    }

    /**
     * Display import results
     *
     * @return void
     */
    protected function displayResults()
    {
        $this->info("\n" . str_repeat('=', 50));
        $this->info('IMPORT COMPLETED');
        $this->info(str_repeat('=', 50));

        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Rows Processed', $this->stats['total_rows']],
                ['New Organisations Imported', $this->stats['imported']],
                ['Existing Organisations Updated', $this->stats['updated']],
                ['Errors Encountered', $this->stats['errors']],
            ]
        );

        if (!empty($this->stats['error_details'])) {
            $this->error("\nError Details:");
            foreach ($this->stats['error_details'] as $error) {
                $this->error("- {$error}");
            }
        }

        $this->info("\nImport process completed successfully!");
    }
}
